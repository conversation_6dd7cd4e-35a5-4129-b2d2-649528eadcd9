package com.frageo.triswipe.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.frageo.triswipe.data.models.GameConfig
import com.frageo.triswipe.data.models.GameStats
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.ui.theme.Dimensions
import com.frageo.triswipe.viewmodel.GameViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.delay

@Composable
fun SettingsSection(
    title: String,
    content: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    ElevatedCard(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.elevatedCardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(Dimensions.getSpacingM())
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = Dimensions.getSpacingM())
            )
            content()
        }
    }
}

@Composable
fun SettingsToggle(
    label: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    description: String? = null,
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = Dimensions.getSpacingXs()),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(top = Dimensions.getSpacingXs())
                )
            }
        }
        val tileAccentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled,
            modifier = Modifier.padding(start = Dimensions.getSpacingM()),
            colors = SwitchDefaults.colors(
                checkedThumbColor = tileAccentColor,
                checkedTrackColor = tileAccentColor.copy(alpha = 0.5f),
                uncheckedBorderColor = tileAccentColor.copy(alpha = 0.3f)
            )
        )
    }
}

@Composable
fun SettingsSlider(
    label: String,
    value: Float,
    onValueChange: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float> = 0f..1f,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    valueFormatter: (Float) -> String = { "${(it * 100).toInt()}%" },
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    val tileAccentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            Text(
                text = valueFormatter(value),
                style = MaterialTheme.typography.bodyMedium,
                color = tileAccentColor
            )
        }
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            enabled = enabled,
            modifier = Modifier.padding(top = Dimensions.getSpacingXs()),
            colors = SliderDefaults.colors(
                thumbColor = tileAccentColor,
                activeTrackColor = tileAccentColor,
                inactiveTrackColor = tileAccentColor.copy(alpha = 0.24f)
            )
        )
    }
}

@Composable
fun TilePreview(
    theme: TileTheme,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        // Show three example tiles with theme colors
        listOf(1, 3, 9).forEach { value ->
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .background(
                        color = Color(android.graphics.Color.parseColor(theme.getTileColor(value))),
                        shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
                    )
            )
        }
    }
}

@Composable
fun TileThemeSelector(
    label: String,
    selectedTheme: TileTheme,
    availableThemes: List<TileTheme>,
    onThemeChange: (TileTheme) -> Unit,
    isPremiumUser: Boolean,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge,
            color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        availableThemes.forEach { theme ->
            val themeEnabled = enabled && (isPremiumUser || !theme.isPremium)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = Dimensions.getSpacingXs()),
                verticalAlignment = Alignment.CenterVertically
            ) {
                val tileAccentColor = Color(android.graphics.Color.parseColor(selectedTheme.getBoardAccentColor()))
                RadioButton(
                    selected = selectedTheme == theme,
                    onClick = { if (themeEnabled) onThemeChange(theme) },
                    enabled = themeEnabled,
                    colors = RadioButtonDefaults.colors(
                        selectedColor = tileAccentColor,
                        unselectedColor = tileAccentColor.copy(alpha = 0.6f)
                    )
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = theme.displayName,
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (themeEnabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        if (theme.isPremium) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "PREMIUM",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .background(
                                        color = MaterialTheme.colorScheme.primaryContainer,
                                        shape = RoundedCornerShape(Dimensions.getCornerRadiusS())
                                    )
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                        Spacer(modifier = Modifier.weight(1f))
                        TilePreview(
                            theme = theme,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                    Text(
                        text = if (themeEnabled) theme.description else "Upgrade to unlock",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (themeEnabled) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f) else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}

@Composable
fun ThemeModeSelector(
    label: String,
    selectedMode: ThemeMode,
    onModeChange: (ThemeMode) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    tileTheme: TileTheme = TileTheme.CLASSIC
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge,
            color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        ThemeMode.values().forEach { mode ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = Dimensions.getSpacingXs()),
                verticalAlignment = Alignment.CenterVertically
            ) {
                val tileAccentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
                RadioButton(
                    selected = selectedMode == mode,
                    onClick = { if (enabled) onModeChange(mode) },
                    enabled = enabled,
                    colors = RadioButtonDefaults.colors(
                        selectedColor = tileAccentColor,
                        unselectedColor = tileAccentColor.copy(alpha = 0.6f)
                    )
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = mode.displayName,
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Text(
                        text = mode.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = if (enabled) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f) else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}

@Composable
fun SettingsButton(
    label: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    description: String? = null,
    buttonText: String = "Action",
    colors: ButtonColors = ButtonDefaults.buttonColors(),
    icon: @Composable (() -> Unit)? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = Dimensions.getSpacingXs()),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = if (enabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
        Button(
            onClick = onClick,
            enabled = enabled,
            colors = colors,
            modifier = Modifier.padding(start = 16.dp)
        ) {
            if (icon != null) {
                icon()
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(
                text = buttonText,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Composable
fun StatisticsDisplay(
    gameStats: GameStats,
    currentSessionTime: Long = 0L,
    isTimerActive: Boolean = false
) {
    // Calculate real-time total time (historical + current session)
    val realTimeTotalTime = gameStats.totalPlayTime + currentSessionTime
    
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        StatisticRow("High Score", gameStats.highScore.toString())
        StatisticRow("Highest Tile", gameStats.bestTile.toString())
        StatisticRow("Games Started", gameStats.gamesPlayed.toString())
        StatisticRow("Games Won", gameStats.gamesWon.toString())
        StatisticRow("Total Moves", gameStats.totalMoves.toString())
        StatisticRow("Average Moves", gameStats.averageMovesPerGame.toString())
        StatisticRow("Total Merges", gameStats.totalMerges.toString())
        StatisticRow("Best Streak", gameStats.longestWinStreak.toString())
        StatisticRow(
            "Total Time", 
            "${realTimeTotalTime / 60000}m ${(realTimeTotalTime / 1000) % 60}s" +
                if (isTimerActive) " ⏱️" else " ⏸️"  // Visual indicator
        )
    }
}

@Composable
private fun StatisticRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = Dimensions.getSpacingXs()),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun ResponsiveSettingsLayout(
    gameViewModel: GameViewModel,
    swipeSensitivity: Float,
    onSwipeSensitivityChange: (Float) -> Unit,
    tapSensitivity: Float,
    onTapSensitivityChange: (Float) -> Unit,
    selectedTheme: TileTheme,
    onThemeChange: (TileTheme) -> Unit,
    themeMode: ThemeMode,
    onThemeModeChange: (ThemeMode) -> Unit,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
    
    if (isLandscape) {
        LandscapeSettingsContent(
            gameViewModel = gameViewModel,
            swipeSensitivity = swipeSensitivity,
            onSwipeSensitivityChange = onSwipeSensitivityChange,
            tapSensitivity = tapSensitivity,
            onTapSensitivityChange = onTapSensitivityChange,
            selectedTheme = selectedTheme,
            onThemeChange = onThemeChange,
            themeMode = themeMode,
            onThemeModeChange = onThemeModeChange,
            modifier = modifier
        )
    } else {
        PortraitSettingsContent(
            gameViewModel = gameViewModel,
            swipeSensitivity = swipeSensitivity,
            onSwipeSensitivityChange = onSwipeSensitivityChange,
            tapSensitivity = tapSensitivity,
            onTapSensitivityChange = onTapSensitivityChange,
            selectedTheme = selectedTheme,
            onThemeChange = onThemeChange,
            themeMode = themeMode,
            onThemeModeChange = onThemeModeChange,
            modifier = modifier
        )
    }
}

@Composable
private fun PortraitSettingsContent(
    gameViewModel: GameViewModel,
    swipeSensitivity: Float,
    onSwipeSensitivityChange: (Float) -> Unit,
    tapSensitivity: Float,
    onTapSensitivityChange: (Float) -> Unit,
    selectedTheme: TileTheme,
    onThemeChange: (TileTheme) -> Unit,
    themeMode: ThemeMode,
    onThemeModeChange: (ThemeMode) -> Unit,
    modifier: Modifier = Modifier
) {
    val gameStats by gameViewModel.getStatisticsFlow().collectAsStateWithLifecycle(initialValue = GameStats())
    val uiState by gameViewModel.uiState.collectAsStateWithLifecycle()
    val isPremiumUser by gameViewModel.getPremiumStatusFlow().collectAsStateWithLifecycle()
    
    // Real-time current session time state
    var currentSessionTime by remember { mutableLongStateOf(0L) }
    
    // Update current session time every second for real-time display
    LaunchedEffect(Unit) {
        while (true) {
            currentSessionTime = gameViewModel.getCurrentSessionPlayTime()
            delay(1000L)
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Game Controls section
        SettingsSection(
            title = "Game Controls",
            content = {
                SettingsSlider(
                    label = "Swipe Sensitivity",
                    value = swipeSensitivity,
                    onValueChange = onSwipeSensitivityChange,
                    tileTheme = selectedTheme
                )
                
                SettingsSlider(
                    label = "Tap Sensitivity",
                    value = tapSensitivity,
                    onValueChange = onTapSensitivityChange,
                    tileTheme = selectedTheme
                )
            }
        )
        
        // Swap Mode section
        SettingsSection(
            title = "Swap Mode",
            content = {
                com.frageo.triswipe.ui.components.SwapModeSettings(
                    gameViewModel = gameViewModel,
                    tileTheme = selectedTheme
                )
            }
        )
        
        // Appearance section
        SettingsSection(
            title = "Appearance",
            content = {
                ThemeModeSelector(
                    label = "App Theme",
                    selectedMode = themeMode,
                    onModeChange = onThemeModeChange,
                    tileTheme = selectedTheme
                )
            }
        )
        
        // Tile Themes section
        SettingsSection(
            title = "Tile Themes",
            content = {
                TileThemeSelector(
                    label = "Choose Tile Theme",
                    selectedTheme = selectedTheme,
                    availableThemes = TileTheme.values().toList(),
                    onThemeChange = onThemeChange,
                    isPremiumUser = isPremiumUser
                )
            }
        )
        
        // Statistics section
        SettingsSection(
            title = "Statistics",
            content = {
                val isTimerActive by remember { 
                    derivedStateOf { gameViewModel.isGameTimerActive() } 
                }
                StatisticsDisplay(gameStats, currentSessionTime, isTimerActive)
            }
        )
        
    }
}

@Composable
private fun LandscapeSettingsContent(
    gameViewModel: GameViewModel,
    swipeSensitivity: Float,
    onSwipeSensitivityChange: (Float) -> Unit,
    tapSensitivity: Float,
    onTapSensitivityChange: (Float) -> Unit,
    selectedTheme: TileTheme,
    onThemeChange: (TileTheme) -> Unit,
    themeMode: ThemeMode,
    onThemeModeChange: (ThemeMode) -> Unit,
    modifier: Modifier = Modifier
) {
    val gameStats by gameViewModel.getStatisticsFlow().collectAsStateWithLifecycle(initialValue = GameStats())
    val uiState by gameViewModel.uiState.collectAsStateWithLifecycle()
    val isPremiumUser by gameViewModel.getPremiumStatusFlow().collectAsStateWithLifecycle()
    
    // Real-time current session time state
    var currentSessionTime by remember { mutableLongStateOf(0L) }
    
    // Update current session time every second for real-time display
    LaunchedEffect(Unit) {
        while (true) {
            currentSessionTime = gameViewModel.getCurrentSessionPlayTime()
            delay(1000L)
        }
    }
    
    Row(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Left column
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Game Controls section
            SettingsSection(
                title = "Game Controls",
                content = {
                    SettingsSlider(
                        label = "Swipe Sensitivity",
                        value = swipeSensitivity,
                        onValueChange = onSwipeSensitivityChange
                    )
                    
                    SettingsSlider(
                        label = "Tap Sensitivity",
                        value = tapSensitivity,
                        onValueChange = onTapSensitivityChange
                    )
                }
            )
            
            // Swap Mode section
            SettingsSection(
                title = "Swap Mode",
                content = {
                    com.frageo.triswipe.ui.components.SwapModeSettings(
                        gameViewModel = gameViewModel
                    )
                }
            )
            
            // Appearance section
            SettingsSection(
                title = "Appearance",
                content = {
                    ThemeModeSelector(
                        label = "App Theme",
                        selectedMode = themeMode,
                        onModeChange = onThemeModeChange
                    )
                }
            )
            
            // Tile Themes section
            SettingsSection(
                title = "Tile Themes",
                content = {
                    TileThemeSelector(
                        label = "Choose Tile Theme",
                        selectedTheme = selectedTheme,
                        availableThemes = TileTheme.values().toList(),
                        onThemeChange = onThemeChange,
                        isPremiumUser = isPremiumUser
                    )
                }
            )
        }
        
        // Right column
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Statistics section
            SettingsSection(
                title = "Statistics",
                content = {
                    val isTimerActive by remember { 
                        derivedStateOf { gameViewModel.isGameTimerActive() } 
                    }
                    StatisticsDisplay(gameStats, currentSessionTime, isTimerActive)
                }
            )
            
        }
    }
}
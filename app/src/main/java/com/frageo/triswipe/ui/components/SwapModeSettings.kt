package com.frageo.triswipe.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.frageo.triswipe.data.models.SwapMode
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.viewmodel.GameViewModel
import kotlinx.coroutines.runBlocking

/**
 * Settings component for selecting swap modes.
 * Shows available modes based on premium status and provides descriptions.
 */
@Composable
fun SwapModeSettings(
    gameViewModel: GameViewModel,
    tileTheme: TileTheme = TileTheme.CLASSIC,
    modifier: Modifier = Modifier
) {
    var currentSwapMode by remember { mutableStateOf(SwapMode.FREE_SWAPPING) }
    val isPremium by gameViewModel.getPremiumStatusFlow().collectAsStateWithLifecycle()
    val availableModes = gameViewModel.getAvailableSwapModes()
    
    // Load current swap mode reactively
    LaunchedEffect(Unit) {
        currentSwapMode = gameViewModel.getSwapMode()
    }
    
    // Update current mode when it changes via the SwapModeManager
    val swapModeState by gameViewModel.getSwapModeState().collectAsStateWithLifecycle()
    LaunchedEffect(swapModeState.currentMode) {
        currentSwapMode = swapModeState.currentMode
    }
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Choose how tile swapping behaves in your games",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Column(
            modifier = Modifier.selectableGroup(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            availableModes.forEach { mode ->
                SwapModeOption(
                    mode = mode,
                    isSelected = mode == currentSwapMode,
                    onSelect = { 
                        currentSwapMode = mode // Update local state immediately
                        gameViewModel.setSwapMode(mode) // Update persistent state
                    },
                    isPremium = isPremium,
                    tileTheme = tileTheme
                )
            }
        }
        
        if (!isPremium) {
            PremiumSwapModesPromo(
                onUpgradeClick = { /* Handle premium upgrade */ }
            )
        }
    }
}

@Composable
private fun SwapModeOption(
    mode: SwapMode,
    isSelected: Boolean,
    onSelect: () -> Unit,
    isPremium: Boolean,
    tileTheme: TileTheme,
    modifier: Modifier = Modifier
) {
    val isEnabled = !mode.isPremiumFeature || isPremium
    val tileAccentColor = Color(android.graphics.Color.parseColor(tileTheme.getBoardAccentColor()))
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .selectable(
                selected = isSelected,
                onClick = { if (isEnabled) onSelect() },
                role = Role.RadioButton,
                enabled = isEnabled
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                tileAccentColor.copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surface
            },
            contentColor = if (isEnabled) {
                MaterialTheme.colorScheme.onSurface
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            }
        ),
        border = if (isSelected) {
            CardDefaults.outlinedCardBorder().copy(
                width = 2.dp,
                brush = androidx.compose.ui.graphics.SolidColor(tileAccentColor)
            )
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = null, // Handled by card click
                enabled = isEnabled,
                colors = RadioButtonDefaults.colors(
                    selectedColor = tileAccentColor,
                    unselectedColor = tileAccentColor.copy(alpha = 0.6f)
                )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = mode.displayName,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium
                    )
                    
                    if (mode.isPremiumFeature) {
                        Spacer(modifier = Modifier.width(8.dp))
                        PremiumBadge()
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = mode.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun PremiumBadge() {
    Surface(
        color = MaterialTheme.colorScheme.tertiary,
        shape = MaterialTheme.shapes.extraSmall,
        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
    ) {
        Text(
            text = "PREMIUM",
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onTertiary,
            modifier = Modifier.padding(horizontal = 4.dp, vertical = 1.dp)
        )
    }
}

@Composable
private fun PremiumSwapModesPromo(
    onUpgradeClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.tertiaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Unlock Advanced Swap Modes",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onTertiaryContainer
            )
            
            Text(
                text = "Get access to strategic swap modes like Merge-Only, One Swap Per Turn, and more with Premium!",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onTertiaryContainer
            )
            
            Button(
                onClick = onUpgradeClick,
                modifier = Modifier.align(Alignment.End)
            ) {
                Text("Upgrade to Premium")
            }
        }
    }
}

/**
 * Compact swap mode status display for the game screen
 */
@Composable
fun SwapModeStatusIndicator(
    statusMessage: String?,
    modifier: Modifier = Modifier
) {
    if (statusMessage != null) {
        Surface(
            modifier = modifier,
            color = MaterialTheme.colorScheme.secondaryContainer,
            shape = MaterialTheme.shapes.small
        ) {
            Text(
                text = statusMessage,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
            )
        }
    }
}
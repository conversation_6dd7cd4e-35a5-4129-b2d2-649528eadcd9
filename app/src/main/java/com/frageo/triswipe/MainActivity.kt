package com.frageo.triswipe

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.frageo.triswipe.viewmodel.GameViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen before super.onCreate()
        installSplashScreen()
        
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // Get GameViewModel from Hilt
        val gameViewModel: GameViewModel by viewModels()
        
        // Pause timer when app goes to background
        lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onPause(owner: LifecycleOwner) {
                gameViewModel.pauseGameTimer()
            }
            
            override fun onResume(owner: LifecycleOwner) {
                // Only resume if on game screen (navigation handler will manage this)
                // This prevents auto-resume when coming back to settings
            }
        })
        
        setContent {
            TriSwipeApp()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun TriSwipeAppPreview() {
    TriSwipeApp()
}
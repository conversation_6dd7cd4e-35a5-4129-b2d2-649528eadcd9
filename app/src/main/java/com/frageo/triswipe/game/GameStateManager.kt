package com.frageo.triswipe.game

import com.frageo.triswipe.animation.AnimationController
import com.frageo.triswipe.data.commands.CommandManager
import com.frageo.triswipe.data.commands.SwipeCommand
import com.frageo.triswipe.data.commands.TileSwapCommand
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.data.models.ImmutableBoard
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.premium.PremiumStatusManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized game state coordination and business logic manager.
 * Handles all game state operations including moves, undo, and game lifecycle.
 * Separates business logic from UI concerns for better testability and maintainability.
 */
@Singleton
class GameStateManager @Inject constructor(
    private val gameEngine: GameEngineInterface,
    private val commandManager: CommandManager,
    private val premiumStatusManager: PremiumStatusManager,
    private val animationController: AnimationController
) {
    
    /**
     * Starts a new game, checking freemium limits and initializing state
     */
    suspend fun startNewGame(): GameStateResult {
        try {
            // Check freemium limits using PremiumStatusManager
            if (!premiumStatusManager.canStartNewGame()) {
                return GameStateResult.failure(
                    gameState = gameEngine.getCurrentGameState(),
                    errorMessage = "No free games remaining. Please upgrade to premium."
                )
            }

            // Decrement free games for non-premium users
            premiumStatusManager.decrementFreeGames()

            val gameState = gameEngine.initializeGame()
            commandManager.clearHistory()
            val gameStats = gameEngine.getGameStats()

            return GameStateResult.success(
                gameState = gameState,
                gameStats = gameStats
            )
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = gameEngine.getCurrentGameState(),
                errorMessage = e.message ?: "Failed to start new game"
            )
        }
    }
    
    /**
     * Restarts the current game without checking freemium limits
     */
    suspend fun restartGame(): GameStateResult {
        try {
            val gameState = gameEngine.initializeGame()
            commandManager.clearHistory()
            val gameStats = gameEngine.getGameStats()

            return GameStateResult.success(
                gameState = gameState,
                gameStats = gameStats
            )
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = gameEngine.getCurrentGameState(),
                errorMessage = e.message ?: "Failed to restart game"
            )
        }
    }
    
    /**
     * Performs a swipe action in the specified direction
     */
    suspend fun performSwipe(direction: Direction): GameStateResult {
        try {
            val currentState = gameEngine.getCurrentGameState()
            if (currentState.isGameOver) {
                return GameStateResult.failure(
                    gameState = currentState,
                    errorMessage = "Cannot swipe - game is over"
                )
            }

            val originalBoard = ImmutableBoard.fromArray(currentState.board)
            val swipeCommand = SwipeCommand(direction)
            val result = commandManager.executeCommand(swipeCommand, gameEngine)
            
            return if (result.success) {
                handleSuccessfulMove(result, direction, originalBoard)
            } else {
                GameStateResult.failure(
                    gameState = gameEngine.getCurrentGameState(),
                    errorMessage = "Move failed: ${result.message}"
                )
            }
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = gameEngine.getCurrentGameState(),
                errorMessage = e.message ?: "Swipe operation failed"
            )
        }
    }
    
    /**
     * Performs a tile swap between two adjacent positions
     */
    suspend fun performTileSwap(pos1: Position, pos2: Position): GameStateResult {
        try {
            val currentState = gameEngine.getCurrentGameState()
            if (currentState.isGameOver) {
                return GameStateResult.failure(
                    gameState = currentState,
                    errorMessage = "Cannot swap tiles - game is over"
                )
            }

            val originalBoard = ImmutableBoard.fromArray(currentState.board)
            val swapCommand = TileSwapCommand(pos1, pos2)
            val result = commandManager.executeCommand(swapCommand, gameEngine)
            
            return if (result.success) {
                handleSuccessfulMove(result, null, originalBoard)
            } else {
                GameStateResult.failure(
                    gameState = gameEngine.getCurrentGameState(),
                    errorMessage = "Swap failed: ${result.message}"
                )
            }
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = gameEngine.getCurrentGameState(),
                errorMessage = e.message ?: "Tile swap operation failed"
            )
        }
    }
    
    /**
     * Performs an undo operation
     */
    suspend fun performUndo(): GameStateResult {
        try {
            val result = commandManager.undoLastCommand(gameEngine)
            
            return if (result.success) {
                val gameStats = gameEngine.getGameStats()
                GameStateResult.success(
                    gameState = result.gameState,
                    gameStats = gameStats
                )
            } else {
                GameStateResult.failure(
                    gameState = gameEngine.getCurrentGameState(),
                    errorMessage = "Undo failed: ${result.message}"
                )
            }
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = gameEngine.getCurrentGameState(),
                errorMessage = e.message ?: "Undo operation failed"
            )
        }
    }
    
    /**
     * Checks if undo is available
     */
    fun canUndo(): Boolean {
        return commandManager.canUndoCommand()
    }
    
    /**
     * Gets the number of available undos
     */
    fun getUndoCount(): Int {
        return commandManager.getUndoableCommandCount()
    }
    
    /**
     * Gets current game state and statistics
     */
    suspend fun getCurrentGameStateResult(): GameStateResult {
        try {
            val gameState = gameEngine.getCurrentGameState()
            val gameStats = gameEngine.getGameStats()
            
            return GameStateResult.success(
                gameState = gameState,
                gameStats = gameStats
            )
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = gameEngine.getCurrentGameState(),
                errorMessage = e.message ?: "Failed to get current game state"
            )
        }
    }
    
    /**
     * Handles successful move results by creating animations and updating state
     */
    private suspend fun handleSuccessfulMove(
        result: com.frageo.triswipe.data.commands.CommandResult,
        direction: Direction?,
        originalBoard: ImmutableBoard
    ): GameStateResult {
        try {
            val gameStats = gameEngine.getGameStats()
            
            // Handle animations if present
            val tileMovements = result.additionalData["tileMovements"] as? List<com.frageo.triswipe.data.models.TileMovement> ?: emptyList()
            val mergeActions = result.additionalData["mergeActions"] as? List<com.frageo.triswipe.data.models.MergeAction> ?: emptyList()
            
            android.util.Log.d("GameStateManager", "Swipe successful - movements: ${tileMovements.size}, merges: ${mergeActions.size}")
            
            // Create unified animations with original board state for proper merge positioning
            val unifiedAnimations = animationController.createUnifiedAnimations(
                tileMovements, 
                mergeActions, 
                direction, 
                originalBoard
            )
            
            // Create visual movements for legacy support
            val visualMovements = if (direction != null) {
                animationController.createVisualMovements(
                    originalBoard,
                    ImmutableBoard.fromArray(result.gameState.board),
                    direction
                )
            } else {
                emptyMap()
            }
            
            return GameStateResult.success(
                gameState = result.gameState,
                animations = unifiedAnimations,
                visualMovements = visualMovements,
                gameStats = gameStats
            )
        } catch (e: Exception) {
            return GameStateResult.failure(
                gameState = result.gameState,
                errorMessage = e.message ?: "Failed to process successful move"
            )
        }
    }
}
package com.frageo.triswipe.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.frageo.triswipe.data.models.GameEngineInterface
import com.frageo.triswipe.data.models.Position
import com.frageo.triswipe.data.models.Direction
import com.frageo.triswipe.data.models.TileTheme
import com.frageo.triswipe.data.models.ThemeMode
import com.frageo.triswipe.data.models.SwapMode
import com.frageo.triswipe.data.models.managers.SwapModeManager
import com.frageo.triswipe.game.GameStateManager
import com.frageo.triswipe.game.GameStateResult
import com.frageo.triswipe.data.repository.GameStateRepository
import com.frageo.triswipe.data.repository.GameStatisticsManager
import com.frageo.triswipe.data.repository.UserPreferencesRepository
import com.frageo.triswipe.billing.BillingManager
import com.frageo.triswipe.premium.PremiumStatusManager
import com.frageo.triswipe.ui.UIStateManager
import com.frageo.triswipe.ui.TileClickAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import android.util.Log

/**
 * Slim GameViewModel focused on coordination between sub-components.
 * 
 * This refactored ViewModel follows the single responsibility principle and acts as
 * a coordinator between:
 * - GameStateManager: Game logic and state management
 * - UIStateManager: UI state and user interactions
 * - GamePersistenceViewModel: Data persistence and settings
 * - PremiumStatusManager: Premium features and billing
 */
@HiltViewModel
class GameViewModel @Inject constructor(
    private val gameStateManager: GameStateManager,
    private val uiStateManager: UIStateManager,
    private val gameEngine: GameEngineInterface,
    private val gameStateRepository: GameStateRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val statisticsManager: GameStatisticsManager,
    private val billingManager: BillingManager,
    private val premiumStatusManager: PremiumStatusManager,
    private val swapModeManager: SwapModeManager,
) : ViewModel() {
    
    // Create sub-components with injected dependencies
    private val gamePersistenceViewModel = GamePersistenceViewModel(gameStateRepository, userPreferencesRepository, statisticsManager, billingManager, viewModelScope)
    
    // Expose UI state from UIStateManager
    val uiState: StateFlow<GameUiState> = uiStateManager.uiState
    
    
    init {
        initializeGame()
        // Initialize swap mode manager
        viewModelScope.launch {
            swapModeManager.initialize()
        }
    }
    
    /**
     * Initialize the game by loading saved state or starting new game
     */
    private fun initializeGame() {
        viewModelScope.launch {
            val savedState = gamePersistenceViewModel.loadGameState()
            if (savedState != null) {
                gameEngine.setGameState(savedState)
                val result = gameStateManager.getCurrentGameStateResult()
                updateFromGameStateResult(result)
            } else {
                val result = gameStateManager.startNewGame()
                updateFromGameStateResult(result)
            }
        }
    }


    
    // ===== Game Actions =====
    
    fun startNewGame() = handleGameAction {
        gamePersistenceViewModel.clearGameState()
        statisticsManager.recordGameStart()
        gameStateManager.startNewGame()
    }

    fun restartGame() = handleGameAction {
        statisticsManager.recordGameStart()
        gameStateManager.restartGame()
    }
    
    fun onSwipeGesture(direction: Direction) = handleGameAction { 
        Log.d("GameViewModel", "Received swipe gesture: $direction")
        // Record move for statistics (non-blocking)
        statisticsManager.recordMove("SWIPE")
        
        uiStateManager.clearSelection()
        val result = gameStateManager.performSwipe(direction)
        Log.d("GameViewModel", "Swipe result: ${if (result.isSuccess()) "success" else "failure - ${result.errorMessage}"}")
        result
    }
    
    fun onTileSwap(position1: Position, position2: Position) = handleGameAction { 
        // Record move for statistics (non-blocking)
        statisticsManager.recordMove("TAP_SWAP")
        
        gameStateManager.performTileSwap(position1, position2) 
    }
    
    fun performUndo() = handleGameAction { 
        gameStateManager.performUndo() 
    }
    
    // ===== UI Actions =====
    
    fun onTileClicked(position: Position) {
        when (val action = uiStateManager.handleTileClick(position)) {
            is TileClickAction.Select -> {
                // Tile selected, no further action needed
            }
            is TileClickAction.Deselect -> {
                // Tile deselected, no further action needed
            }
            is TileClickAction.Swap -> {
                onTileSwap(action.position1, action.position2)
            }
        }
    }
    
    fun clearSelection() = uiStateManager.clearSelection()
    
    fun clearError() = uiStateManager.clearError()
    
    // ===== Game State Queries =====
    
    fun canUndo() = gameStateManager.canUndo()
    fun getUndoCount() = gameStateManager.getUndoCount()
    
    /**
     * Handles game actions using GameStateResult pattern with proper error handling and loading states
     */
    private fun handleGameAction(action: suspend () -> GameStateResult) {
        viewModelScope.launch {
            uiStateManager.setLoading(true)
            try {
                val result = action()
                updateFromGameStateResult(result)
                if (result.isSuccess()) {
                    gamePersistenceViewModel.saveGameState(result.gameState)
                }
            } catch (e: Exception) {
                uiStateManager.setError(e.message ?: "Operation failed")
            }
        }
    }
    
    /**
     * Updates UI state from GameStateResult and tracks statistics
     */
    private suspend fun updateFromGameStateResult(result: GameStateResult) {
        Log.d("GameViewModel", "Updating UI from GameStateResult - success: ${result.isSuccess()}, animations: ${result.animations.size}")
        
        val selectedTheme = gamePersistenceViewModel.getSelectedTheme()
        
        // Update UI state via UIStateManager
        uiStateManager.updateFromGameStateResult(result)
        uiStateManager.updateUndoStatus(gameStateManager.canUndo())
        uiStateManager.updateTheme(selectedTheme)
        
        // Track game completion if game is over (non-blocking)
        if (result.gameState.isGameOver || result.gameState.hasWon) {
            Log.d("GameViewModel", "Game ended - isGameOver: ${result.gameState.isGameOver}, hasWon: ${result.gameState.hasWon}")
            val playTimeMs = gameEngine.getPlayTimeMs()
            val totalMerges = gameEngine.getTotalMerges()

            statisticsManager.recordGameCompletion(
                gameState = result.gameState,
                playTime = playTimeMs,
                merges = totalMerges
            )
        }
        
        // Note: Individual merge tracking removed - merges are properly tracked 
        // in onGameCompleted() using GameEngine.getTotalMerges() which counts actual merges
    }
    
    // ===== Statistics Delegation =====
    
    fun getStatisticsFlow() = gamePersistenceViewModel.getStatisticsFlow()
    
    /**
     * Get current session play time in milliseconds for real-time display
     */
    fun getCurrentSessionPlayTime(): Long = gameEngine.getPlayTimeMs()
    
    /**
     * Pause the game timer to stop time accumulation
     */
    fun pauseGameTimer() = gameEngine.pauseGameTimer()
    
    /**
     * Resume the game timer to start time accumulation
     */
    fun resumeGameTimer() = gameEngine.resumeGameTimer()
    
    /**
     * Check if the game timer is currently active
     */
    fun isGameTimerActive(): Boolean = gameEngine.isTimerActive()

    // ===== Billing & Premium Delegation =====
    
    fun launchPurchaseFlow(activity: android.app.Activity) = gamePersistenceViewModel.launchPurchaseFlow(activity)
    fun getBillingState() = gamePersistenceViewModel.getBillingState()
    fun getPurchaseState() = gamePersistenceViewModel.getPurchaseState()
    fun getPremiumPrice() = gamePersistenceViewModel.getPremiumPrice()
    fun getPremiumStatusFlow() = premiumStatusManager.isPremiumUser
    fun getFreeGamesRemainingFlow() = premiumStatusManager.freeGamesRemaining
    
    fun simulatePremiumUnlock() = viewModelScope.launch { 
        premiumStatusManager.setPremiumStatus(true) 
    }
    
    fun resetPremiumStatus() = viewModelScope.launch { 
        premiumStatusManager.resetToFreeUser() 
    }
    
    // ===== Theme Delegation =====
    
    fun setSelectedTheme(theme: TileTheme) {
        gamePersistenceViewModel.setSelectedTheme(theme)
        uiStateManager.updateTheme(theme)
    }
    
    suspend fun getSelectedTheme(): TileTheme = gamePersistenceViewModel.getSelectedTheme()
    fun getAvailableThemes() = gamePersistenceViewModel.getAvailableThemes()
    
    fun getThemeModeFlow() = gamePersistenceViewModel.themeMode
    fun setThemeMode(themeMode: ThemeMode) = gamePersistenceViewModel.setThemeMode(themeMode)
    suspend fun getThemeMode(): ThemeMode = gamePersistenceViewModel.getThemeModeOnce()
    
    /**
     * Save current game state (for navigation/lifecycle events)
     */
    fun saveCurrentGameState() {
        viewModelScope.launch {
            try {
                val currentState = gameEngine.getCurrentGameState()
                gamePersistenceViewModel.saveGameState(currentState)
            } catch (e: Exception) {
                // Silently handle save errors to avoid disrupting user flow
            }
        }
    }
    
    // ===== Swap Mode Delegation =====
    
    fun getSwapModeState() = swapModeManager.swapModeState
    
    fun setSwapMode(swapMode: SwapMode) = viewModelScope.launch {
        swapModeManager.setSwapMode(swapMode)
    }
    
    suspend fun getSwapMode(): SwapMode = userPreferencesRepository.getSwapMode()
    
    fun getAvailableSwapModes(): List<SwapMode> {
        val isPremium = premiumStatusManager.isPremiumUser.value
        return SwapMode.getAvailableModes(isPremium)
    }
    
}
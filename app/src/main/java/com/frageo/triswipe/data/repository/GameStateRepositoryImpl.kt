package com.frageo.triswipe.data.repository

import com.frageo.triswipe.data.database.GameDao
import com.frageo.triswipe.data.models.GameState
import com.frageo.triswipe.data.repository.entities.GameStateEntity
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of GameStateRepository for game state persistence operations.
 * Uses Room database for local storage of game state data.
 */
@Singleton
class GameStateRepositoryImpl @Inject constructor(
    private val gameDao: GameDao
) : GameStateRepository {
    
    override suspend fun saveGameState(gameState: GameState) {
        val entity = GameStateEntity(
            board = gameState.board,
            score = gameState.score,
            moves = gameState.moves,
            isGameOver = gameState.isGameOver,
            hasWon = gameState.hasWon,
            lastMoveType = gameState.lastMoveType,
            cumulativePlayTime = gameState.cumulativePlayTime,
            isTimerActive = gameState.isTimerActive
        )
        
        gameDao.insertGameState(entity)
    }
    
    override suspend fun loadGameState(): GameState? {
        val entity = gameDao.getGameState()
        return entity?.let {
            GameState(
                board = it.board,
                score = it.score,
                moves = it.moves,
                isGameOver = it.isGameOver,
                hasWon = it.hasWon,
                lastMoveType = it.lastMoveType,
                cumulativePlayTime = it.cumulativePlayTime,
                isTimerActive = it.isTimerActive
            )
        }
    }
    
    override suspend fun clearGameState() {
        gameDao.deleteGameState()
    }
}
package com.frageo.triswipe.data.models

import com.frageo.triswipe.data.commands.GameStateSnapshot
import com.frageo.triswipe.data.models.managers.UndoManager
import com.frageo.triswipe.data.models.managers.MoveAnalyzer
import com.frageo.triswipe.data.models.managers.GameValidator
import com.frageo.triswipe.data.models.managers.MoveExecutor
import com.frageo.triswipe.data.models.managers.MoveParams
import com.frageo.triswipe.data.models.managers.SwapModeManager
import com.frageo.triswipe.data.repository.GameStatisticsManager
import javax.inject.Inject

/**
 * Internal atomic state container for GameEngine.
 * All mutable state properties are encapsulated here to enable atomic updates
 * and prevent race conditions during multi-property state changes.
 */
private data class InternalGameState(
    val score: Int = 0,
    val moves: Int = 0,
    val isGameOver: Boolean = false,
    val hasWon: Boolean = false,
    val totalMerges: Int = 0,
    val gameStartTime: Long = 0L,
    val cumulativePlayTime: Long = 0L,      // Total gameplay time accumulated (ms)
    val isTimerActive: Boolean = false,      // Is timer currently running?
    val lastResumeTime: Long = 0L           // When timer was last resumed (for current active session)
) {
    
    /**
     * Create a new state with updated score
     */
    fun withScore(newScore: Int): InternalGameState = copy(score = newScore)
    
    /**
     * Create a new state with incremented moves
     */
    fun withMovesIncremented(): InternalGameState = copy(moves = moves + 1)
    
    /**
     * Create a new state with updated game status
     */
    fun withGameStatus(gameOver: Boolean, won: Boolean): InternalGameState = 
        copy(isGameOver = gameOver, hasWon = won)
    
    /**
     * Create a new state with additional merges
     */
    fun withMergesAdded(additionalMerges: Int): InternalGameState = 
        copy(totalMerges = totalMerges + additionalMerges)
    
    /**
     * Create a new state from a GameState result
     */
    fun fromGameStateResult(gameState: GameState, additionalMerges: Int = 0): InternalGameState = 
        copy(
            score = gameState.score,
            moves = gameState.moves,
            isGameOver = gameState.isGameOver,
            hasWon = gameState.hasWon,
            totalMerges = totalMerges + additionalMerges
        )
    
    /**
     * Reset state for new game with timer started, preserving cumulative play time
     */
    fun resetForNewGame(): InternalGameState = 
        InternalGameState(
            gameStartTime = System.currentTimeMillis(),
            isTimerActive = true,
            lastResumeTime = System.currentTimeMillis(),
            cumulativePlayTime = this.cumulativePlayTime  // Preserve accumulated time
        )
    
    /**
     * Convert to external GameState representation
     */
    fun toGameState(board: Array<Array<Tile?>>): GameState = 
        GameState(
            board = board,
            score = score,
            moves = moves,
            isGameOver = isGameOver,
            hasWon = hasWon,
            cumulativePlayTime = cumulativePlayTime,
            isTimerActive = isTimerActive
        )
    
    /**
     * Get cumulative play time in milliseconds
     */
    fun getPlayTimeMs(): Long = if (isTimerActive && lastResumeTime > 0) {
        cumulativePlayTime + (System.currentTimeMillis() - lastResumeTime)
    } else {
        cumulativePlayTime
    }
    
    /**
     * Start/resume the game timer
     */
    fun startTimer(): InternalGameState = copy(
        gameStartTime = if (gameStartTime == 0L) System.currentTimeMillis() else gameStartTime,
        isTimerActive = true,
        lastResumeTime = System.currentTimeMillis()
    )
    
    /**
     * Pause the game timer, accumulating the current session time
     */
    fun pauseTimer(): InternalGameState = copy(
        cumulativePlayTime = if (isTimerActive && lastResumeTime > 0) {
            cumulativePlayTime + (System.currentTimeMillis() - lastResumeTime)
        } else {
            cumulativePlayTime
        },
        isTimerActive = false,
        lastResumeTime = 0L
    )
}

class GameEngine @Inject constructor(
    private val undoManager: UndoManager,
    private val moveAnalyzer: MoveAnalyzer,
    private val gameValidator: GameValidator,
    private val moveExecutor: MoveExecutor,
    private val mergeDetector: MergeDetector,
    private val mergeExecutor: MergeExecutor,
    private val statisticsManager: GameStatisticsManager,
    private val swapModeManager: SwapModeManager
) : GameEngineInterface {
    private val board = GameBoard()
    
    // Atomic state container with thread-safe access
    private var internalState = InternalGameState()
    
    /**
     * Atomically update the internal game state.
     * All state property updates must go through this method to ensure thread safety.
     */
    @Synchronized
    private fun updateGameState(transform: (InternalGameState) -> InternalGameState) {
        internalState = transform(internalState)
    }
    
    /**
     * Thread-safe read access to current state.
     */
    @Synchronized
    private fun readGameState(): InternalGameState = internalState
    
    override fun initializeGame(): GameState {
        board.initializeBoard()
        
        // Atomically reset game state for new game
        updateGameState { it.resetForNewGame() }
        
        // Clear undo history for new game
        undoManager.clearHistory()
        
        // Reset swap mode state for new game
        swapModeManager.resetForNewGame()
        
        return getCurrentGameState()
    }
    
    override fun performSwipe(direction: Direction): SwipeResult {
        // Check if game is over first
        if (readGameState().isGameOver) {
            return SwipeResult(
                gameState = getCurrentGameState(),
                success = false,
                message = "Game is over"
            )
        }
        
        // Save current state for undo functionality
        saveCurrentStateToHistory()
        
        // Execute swipe using MoveExecutor
        val result = moveExecutor.executeSwipe(direction, getCurrentGameState(), board)
        
        // Update internal game state from result
        if (result.success) {
            // Atomically update all state properties from result
            updateGameState { currentState ->
                currentState.fromGameStateResult(result.gameState, result.mergeActions.size)
            }

            // Update swap mode state after swipe
            swapModeManager.onSwipePerformed()
            
            // Check for tile upgrades and notify swap mode manager
            checkForTileUpgrades(result.mergeActions)

            // Emit merge events for statistics tracking
            emitMergeEvents(result.mergeActions)
            
            // Record score update for real-time high score tracking
            val currentScore = readGameState().score
            statisticsManager.recordScoreUpdate(currentScore)

            // Check win and game over conditions
            checkWinCondition()
            checkGameOverCondition()
            
            // Return result with updated game state
            return SwipeResult(
                gameState = getCurrentGameState(),
                success = result.success,
                message = result.message,
                tileMovements = result.tileMovements,
                mergeActions = result.mergeActions,
                scoreGained = result.scoreGained
            )
        }
        
        return result
    }
    
    override fun performTileSwap(pos1: Position, pos2: Position): SwapResult {
        // Check if swap is allowed by current swap mode
        val swapValidation = swapModeManager.isSwapAllowed(pos1, pos2, board, getCurrentGameState())
        if (!swapValidation.isValid) {
            return SwapResult(
                gameState = getCurrentGameState(),
                success = false,
                message = swapValidation.reason
            )
        }
        
        // Save current state for undo functionality
        saveCurrentStateToHistory()
        
        // Execute swap using MoveExecutor
        val result = moveExecutor.executeSwap(pos1, pos2, getCurrentGameState(), board)
        
        // Update internal game state from result
        if (result.success) {
            // Atomically update all state properties from result
            updateGameState { currentState ->
                currentState.fromGameStateResult(result.gameState, result.mergeActions.size)
            }

            // Update swap mode state after successful swap
            swapModeManager.onSwapPerformed(result.mergeActions)
            
            // Check for tile upgrades and notify swap mode manager
            checkForTileUpgrades(result.mergeActions)

            // Emit merge events for statistics tracking
            emitMergeEvents(result.mergeActions)
            
            // Record score update for real-time high score tracking
            val currentScore = readGameState().score
            statisticsManager.recordScoreUpdate(currentScore)

            // Check win and game over conditions
            checkWinCondition()
            checkGameOverCondition()
            
            // Return result with updated game state
            return SwapResult(
                gameState = getCurrentGameState(),
                success = result.success,
                message = result.message,
                mergeActions = result.mergeActions,
                scoreGained = result.scoreGained
            )
        }
        
        return result
    }
    
    private fun checkWinCondition() {
        val currentState = readGameState()
        if (!currentState.hasWon && mergeExecutor.checkWinCondition(board)) {
            updateGameState { it.copy(hasWon = true) }
        }
    }
    
    private fun checkGameOverCondition() {
        if (!mergeDetector.canMakeMoves(board)) {
            updateGameState { it.copy(isGameOver = true) }
        }
    }
    
    override fun getCurrentGameState(): GameState {
        val currentState = readGameState()
        return currentState.toGameState(board.copyBoard())
    }
    
    override fun setGameState(gameState: GameState) {
        board.restoreFromArray(gameState.board)
        
        // Atomically set the game state from external source
        updateGameState { 
            InternalGameState(
                score = gameState.score,
                moves = gameState.moves,
                isGameOver = gameState.isGameOver,
                hasWon = gameState.hasWon,
                totalMerges = 0, // Reset tracking for restored game
                gameStartTime = System.currentTimeMillis(),
                cumulativePlayTime = gameState.cumulativePlayTime, // Restore cumulative time
                isTimerActive = gameState.isTimerActive,           // Restore timer state
                lastResumeTime = if (gameState.isTimerActive) System.currentTimeMillis() else 0L
            )
        }
    }
    
    // Undo functionality methods (premium feature) - delegated to UndoManager
    private fun saveCurrentStateToHistory() {
        val currentState = readGameState()
        val snapshot = GameStateSnapshot(
            boardState = board.copyBoard(),
            score = currentState.score,
            moves = currentState.moves,
            isGameOver = currentState.isGameOver,
            hasWon = currentState.hasWon,
            totalMerges = currentState.totalMerges
        )
        undoManager.saveState(snapshot)
    }
    
    override fun canUndo(): Boolean {
        return undoManager.canUndo()
    }
    
    override fun getUndoCount(): Int {
        return undoManager.getUndoCount()
    }
    
    override fun performUndo(): UndoResult {
        val result = undoManager.performUndo()
        
        if (result.success) {
            // Restore the game state from the undo result
            board.restoreFromArray(result.gameState.board)
            
            // Atomically restore state from undo result (preserve totalMerges for session tracking)
            updateGameState { currentState ->
                currentState.copy(
                    score = result.gameState.score,
                    moves = result.gameState.moves,
                    isGameOver = result.gameState.isGameOver,
                    hasWon = result.gameState.hasWon
                    // Note: totalMerges is not restored to maintain current session tracking
                )
            }
        }
        
        return UndoResult(
            success = result.success,
            message = result.message,
            gameState = getCurrentGameState()
        )
    }
    
    fun clearUndoHistory() {
        undoManager.clearHistory()
    }
    
    fun updateTileOrigin(position: Position, newOrigin: TileOrigin) {
        board.updateTileOrigin(position, newOrigin)
    }
    
    fun validateGameState(): GameValidationResult {
        return gameValidator.validateGameState(getCurrentGameState(), board)
    }
    
    fun getAvailableMoves(): List<AvailableMove> {
        return moveAnalyzer.getAvailableMoves(board)
    }
    
    
    fun getBestMove(): AvailableMove? {
        return moveAnalyzer.getBestMove(board)
    }
    
    override fun getGameStats(): GameStatistics {
        val currentState = readGameState()
        return GameStatistics(
            score = currentState.score,
            moves = currentState.moves,
            highestTile = mergeExecutor.getHighestTileValue(board),
            emptySpaces = board.getEmptyPositions().size,
            availableMoves = moveAnalyzer.getAvailableMovesCount(board),
            canWin = !currentState.hasWon && mergeExecutor.checkWinCondition(board)
        )
    }
    
    override fun getTotalMerges(): Int = readGameState().totalMerges
    
    override fun getPlayTimeMs(): Long = readGameState().getPlayTimeMs()
    
    /**
     * Pause the game timer, stopping time accumulation
     */
    override fun pauseGameTimer() {
        updateGameState { it.pauseTimer() }
    }
    
    /**
     * Resume the game timer, starting time accumulation
     */
    override fun resumeGameTimer() {
        updateGameState { it.startTimer() }
    }
    
    /**
     * Check if the game timer is currently active
     */
    override fun isTimerActive(): Boolean = readGameState().isTimerActive
    
    fun performValidatedMove(moveType: MoveType, direction: Direction? = null, pos1: Position? = null, pos2: Position? = null): MoveResult {
        // Create move parameters based on type
        val params = when (moveType) {
            MoveType.SWIPE -> {
                if (direction == null) {
                    return MoveResult(
                        success = false,
                        message = "Direction required for swipe",
                        gameState = getCurrentGameState()
                    )
                }
                MoveParams.SwipeParams(direction)
            }
            MoveType.SWAP -> {
                if (pos1 == null || pos2 == null) {
                    return MoveResult(
                        success = false,
                        message = "Both positions required for swap",
                        gameState = getCurrentGameState()
                    )
                }
                MoveParams.SwapParams(pos1, pos2)
            }
        }
        
        // Save state for undo
        saveCurrentStateToHistory()
        
        // Execute validated move using MoveExecutor
        val result = moveExecutor.executeValidatedMove(moveType, params, getCurrentGameState(), board)
        
        // Update internal game state from result
        if (result.success) {
            // Atomically update all state properties from result
            updateGameState { currentState ->
                currentState.fromGameStateResult(result.gameState, result.mergeActions.size)
            }

            // Emit merge events for statistics tracking
            emitMergeEvents(result.mergeActions)
            
            // Record score update for real-time high score tracking
            val currentScore = readGameState().score
            statisticsManager.recordScoreUpdate(currentScore)

            // Check win and game over conditions
            checkWinCondition()
            checkGameOverCondition()
            
            // Return result with updated game state
            return MoveResult(
                success = result.success,
                message = result.message,
                gameState = getCurrentGameState(),
                tileMovements = result.tileMovements,
                mergeActions = result.mergeActions,
                scoreGained = result.scoreGained
            )
        }
        
        return result
    }
    
    fun restartGame(): GameState {
        return initializeGame()
    }

    /**
     * Emits merge events for statistics tracking.
     * This connects the game engine to the event-driven statistics system.
     */
    private fun emitMergeEvents(mergeActions: List<MergeAction>) {
        for (mergeAction in mergeActions) {
            // Record the tile value that was created by the merge
            statisticsManager.recordMerge(mergeAction.createdTile.value)
        }
    }
    
    /**
     * Check for tile upgrades and notify swap mode manager
     */
    private fun checkForTileUpgrades(mergeActions: List<MergeAction>) {
        val currentMoves = readGameState().moves
        for (mergeAction in mergeActions) {
            swapModeManager.onTileUpgrade(mergeAction.createdTile.value, currentMoves)
        }
    }
    
    /**
     * Get valid swap positions for the selected tile based on current swap mode
     */
    override fun getValidSwapPositions(selectedPosition: Position): List<Position> {
        return swapModeManager.getValidSwapPositions(selectedPosition, board, getCurrentGameState())
    }
    
    /**
     * Get current swap mode status message
     */
    override fun getSwapModeStatus(): String? {
        return swapModeManager.getStatusMessage()
    }
}

data class SwipeResult(
    val gameState: GameState,
    val success: Boolean,
    val message: String,
    val tileMovements: List<TileMovement> = emptyList(),
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0
)

data class SwapResult(
    val gameState: GameState,
    val success: Boolean,
    val message: String,
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0
)

data class AvailableMove(
    val type: MoveType,
    val direction: Direction? = null,
    val position1: Position? = null,
    val position2: Position? = null,
    val estimatedScore: Int = 0,
    val tileMovements: Int = 0
)

enum class MoveType {
    SWIPE, SWAP
}

data class GameStatistics(
    val score: Int,
    val moves: Int,
    val highestTile: Int,
    val emptySpaces: Int,
    val availableMoves: Int,
    val canWin: Boolean
)

data class GameValidationResult(
    val isValid: Boolean,
    val boardValidation: BoardValidationResult,
    val gameErrors: List<String>,
    val gameState: GameState
) {
    fun getAllErrors(): List<String> {
        return boardValidation.errors + gameErrors
    }
    
    fun getAllWarnings(): List<String> {
        return boardValidation.warnings
    }
}

data class MoveResult(
    val success: Boolean,
    val message: String,
    val gameState: GameState,
    val tileMovements: List<TileMovement> = emptyList(),
    val mergeActions: List<MergeAction> = emptyList(),
    val scoreGained: Int = 0
)


// Result class for undo operations
data class UndoResult(
    val success: Boolean,
    val message: String,
    val gameState: GameState
)
package com.frageo.triswipe.data.models

/**
 * Interface for game engine operations.
 * Enables testing and provides abstraction for different game engine implementations.
 */
interface GameEngineInterface {
    /**
     * Initialize a new game
     */
    fun initializeGame(): GameState
    
    /**
     * Perform a swipe action
     */
    fun performSwipe(direction: Direction): SwipeResult
    
    /**
     * Perform a tile swap action
     */
    fun performTileSwap(pos1: Position, pos2: Position): SwapResult
    
    /**
     * Get the current game state
     */
    fun getCurrentGameState(): GameState
    
    /**
     * Set the game state (for loading/restoring)
     */
    fun setGameState(gameState: GameState)
    
    /**
     * Check if undo is available
     */
    fun canUndo(): Boolean
    
    /**
     * Perform an undo operation
     */
    fun performUndo(): UndoResult
    
    /**
     * Get the number of available undos
     */
    fun getUndoCount(): Int
    
    /**
     * Get current game statistics
     */
    fun getGameStats(): GameStatistics
    
    /**
     * Get the total number of merges performed in current game
     */
    fun getTotalMerges(): Int
    
    /**
     * Get the play time in milliseconds for current game
     */
    fun getPlayTimeMs(): Long
    
    /**
     * Pause the game timer, stopping time accumulation
     */
    fun pauseGameTimer()
    
    /**
     * Resume the game timer, starting time accumulation
     */
    fun resumeGameTimer()
    
    /**
     * Check if the game timer is currently active
     */
    fun isTimerActive(): Boolean
    
    /**
     * Get valid swap positions for the selected tile based on current swap mode
     */
    fun getValidSwapPositions(selectedPosition: Position): List<Position>
    
    /**
     * Get current swap mode status message
     */
    fun getSwapModeStatus(): String?
}
package com.frageo.triswipe.data.repository.entities

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.frageo.triswipe.data.database.Converters
import com.frageo.triswipe.data.models.Tile

@Entity(tableName = "game_state")
@TypeConverters(Converters::class)
data class GameStateEntity(
    @PrimaryKey
    val id: String = "current_game",
    val board: Array<Array<Tile?>>,
    val score: Int,
    val moves: Int,
    val isGameOver: Boolean,
    val hasWon: Boolean,
    val lastMoveType: String?,
    val cumulativePlayTime: Long = 0L,  // Timer: cumulative play time in milliseconds
    val isTimerActive: Boolean = false, // Timer: whether timer is currently active
    val timestamp: Long = System.currentTimeMillis()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GameStateEntity

        if (id != other.id) return false
        if (!board.contentDeepEquals(other.board)) return false
        if (score != other.score) return false
        if (moves != other.moves) return false
        if (isGameOver != other.isGameOver) return false
        if (hasWon != other.hasWon) return false
        if (lastMoveType != other.lastMoveType) return false
        if (cumulativePlayTime != other.cumulativePlayTime) return false
        if (isTimerActive != other.isTimerActive) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + board.contentDeepHashCode()
        result = 31 * result + score
        result = 31 * result + moves
        result = 31 * result + isGameOver.hashCode()
        result = 31 * result + hasWon.hashCode()
        result = 31 * result + (lastMoveType?.hashCode() ?: 0)
        result = 31 * result + cumulativePlayTime.hashCode()
        result = 31 * result + isTimerActive.hashCode()
        return result
    }
}
package com.frageo.triswipe.data.models

import com.frageo.triswipe.data.models.managers.*
import com.frageo.triswipe.data.repository.GameStatisticsManager
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any

/**
 * Simple test class for GameEngine timer functionality.
 * Tests the basic pause/resume timer system.
 */
class GameEngineTimerSimpleTest {

    @Mock private lateinit var undoManager: UndoManager
    @Mock private lateinit var moveAnalyzer: MoveAnalyzer
    @Mock private lateinit var gameValidator: GameValidator
    @Mock private lateinit var moveExecutor: MoveExecutor
    @Mock private lateinit var mergeDetector: MergeDetector
    @Mock private lateinit var mergeExecutor: MergeExecutor
    @Mock private lateinit var statisticsManager: GameStatisticsManager
    @Mock private lateinit var swapModeManager: SwapModeManager

    private lateinit var gameEngine: GameEngine

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // Mock basic dependencies with minimal setup
        `when`(moveAnalyzer.getAvailableMovesCount(any())).thenReturn(5)
        `when`(gameValidator.validateGameState(any(), any())).thenReturn(
            GameValidationResult(
                isValid = true, 
                boardValidation = BoardValidationResult(isValid = true, errors = emptyList(), warnings = emptyList(), tileCount = 16, emptySpaceCount = 0),
                gameErrors = emptyList(),
                gameState = GameState(board = Array(4) { Array(4) { null } }, score = 0, moves = 0, isGameOver = false, hasWon = false)
            )
        )
        `when`(mergeExecutor.getHighestTileValue(any())).thenReturn(27)
        `when`(mergeExecutor.checkWinCondition(any())).thenReturn(false)
        `when`(mergeDetector.canMakeMoves(any())).thenReturn(true)
        
        gameEngine = GameEngine(
            undoManager, moveAnalyzer, gameValidator, moveExecutor,
            mergeDetector, mergeExecutor, statisticsManager, swapModeManager
        )
    }

    @Test
    fun `timer starts active when new game is created`() {
        // Act
        gameEngine.restartGame()
        
        // Assert
        assertTrue("Timer should be active after starting new game", gameEngine.isTimerActive())
        assertTrue("Play time should be non-negative", gameEngine.getPlayTimeMs() >= 0)
    }

    @Test
    fun `pause timer stops activity`() {
        // Arrange
        gameEngine.restartGame()
        assertTrue("Timer should start active", gameEngine.isTimerActive())
        
        // Act
        gameEngine.pauseGameTimer()
        
        // Assert
        assertFalse("Timer should be inactive after pause", gameEngine.isTimerActive())
    }

    @Test
    fun `resume timer starts activity again`() {
        // Arrange
        gameEngine.restartGame()
        gameEngine.pauseGameTimer()
        assertFalse("Timer should be paused", gameEngine.isTimerActive())
        
        // Act
        gameEngine.resumeGameTimer()
        
        // Assert
        assertTrue("Timer should be active after resume", gameEngine.isTimerActive())
    }

    @Test
    fun `new game resets timer and starts it`() {
        // Arrange
        gameEngine.restartGame()
        gameEngine.pauseGameTimer()
        assertFalse("Timer should be paused", gameEngine.isTimerActive())
        
        // Act
        gameEngine.restartGame()
        
        // Assert
        assertTrue("Timer should be active after restart", gameEngine.isTimerActive())
        assertEquals("Time should be 0 immediately after restart", 0L, gameEngine.getPlayTimeMs())
    }

    @Test
    fun `timer returns accumulated time when paused`() {
        // Arrange
        gameEngine.restartGame()
        
        // Act
        gameEngine.pauseGameTimer()
        val pausedTime1 = gameEngine.getPlayTimeMs()
        val pausedTime2 = gameEngine.getPlayTimeMs()
        
        // Assert
        assertFalse("Timer should be inactive", gameEngine.isTimerActive())
        assertEquals("Paused timer should return same time consistently", pausedTime1, pausedTime2)
    }
}
package com.frageo.triswipe.data.models

import com.frageo.triswipe.data.models.managers.UndoManager
import com.frageo.triswipe.data.models.managers.MoveAnalyzer
import com.frageo.triswipe.data.models.managers.GameValidator
import com.frageo.triswipe.data.models.managers.MoveExecutor
import com.frageo.triswipe.data.repository.GameStatisticsManager
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.never
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.random.Random

class GameEngineTest {

    @Mock
    private lateinit var undoManager: UndoManager

    @Mock
    private lateinit var moveAnalyzer: MoveAnalyzer

    @Mock
    private lateinit var gameValidator: GameValidator

    @Mock
    private lateinit var moveExecutor: MoveExecutor

    @Mock
    private lateinit var mergeDetector: MergeDetector

    @Mock
    private lateinit var mergeExecutor: MergeExecutor

    @Mock
    private lateinit var statisticsManager: GameStatisticsManager

    private lateinit var gameEngine: GameEngine

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        val mockSwapModeManager = org.mockito.kotlin.mock<com.frageo.triswipe.data.models.managers.SwapModeManager>()
        gameEngine = GameEngine(
            undoManager,
            moveAnalyzer,
            gameValidator,
            moveExecutor,
            mergeDetector,
            mergeExecutor,
            statisticsManager,
            mockSwapModeManager
        )
    }

    @Test
    fun `initializeGame should initialize game state without recording start`() {
        val gameState = gameEngine.initializeGame()
        
        // Verify game state is properly initialized
        assertFalse(gameState.isGameOver)
        assertFalse(gameState.hasWon)
        assertEquals(0, gameState.score)
        assertEquals(0, gameState.moves)
        
        // Verify that recordGameStart is not called at the engine level
        // (it should be called at the ViewModel level instead)
        verify(statisticsManager, never()).recordGameStart()
    }

    @Test
    fun `atomic state updates should prevent race conditions during concurrent access`() {
        // Initialize game engine
        gameEngine.initializeGame()
        
        // Mock a successful swipe result with state changes
        val mockResult = SwipeResult(
            gameState = GameState(
                board = Array(4) { Array(4) { null } },
                score = 100,
                moves = 5,
                isGameOver = false,
                hasWon = false
            ),
            success = true,
            message = "Success",
            mergeActions = listOf(
                MergeAction(
                    removedTiles = listOf(),
                    createdTile = Tile(3, Position(0, 0)),
                    scoreGained = 10,
                    mergePosition = Position(0, 0)
                )
            )
        )
        
        `when`(moveExecutor.executeSwipe(
            org.mockito.kotlin.any(), 
            org.mockito.kotlin.any(), 
            org.mockito.kotlin.any()
        )).thenReturn(mockResult)
        
        `when`(mergeExecutor.checkWinCondition(org.mockito.kotlin.any())).thenReturn(false)
        `when`(mergeDetector.canMakeMoves(org.mockito.kotlin.any())).thenReturn(true)
        
        val executor = Executors.newFixedThreadPool(10)
        val latch = CountDownLatch(100)
        val results = mutableListOf<GameState>()
        val exceptions = mutableListOf<Exception>()
        
        // Launch 100 concurrent swipe operations
        repeat(100) {
            executor.submit {
                try {
                    val result = gameEngine.performSwipe(Direction.UP)
                    synchronized(results) {
                        results.add(result.gameState)
                    }
                } catch (e: Exception) {
                    synchronized(exceptions) {
                        exceptions.add(e)
                    }
                } finally {
                    latch.countDown()
                }
            }
        }
        
        // Wait for all operations to complete
        assertTrue("Operations should complete within 5 seconds", 
                  latch.await(5, TimeUnit.SECONDS))
        
        // Verify no exceptions occurred
        assertTrue("No exceptions should occur during concurrent access: ${exceptions.joinToString()}", 
                  exceptions.isEmpty())
        
        // Verify all results have consistent state (all should reflect the updates)
        results.forEach { state ->
            assertEquals("All results should have consistent score", 100, state.score)
            assertEquals("All results should have consistent moves", 5, state.moves)
            assertFalse("All results should have consistent game over status", state.isGameOver)
            assertFalse("All results should have consistent win status", state.hasWon)
        }
        
        // Verify final state is also consistent
        val finalState = gameEngine.getCurrentGameState()
        assertEquals("Final state should have correct score", 100, finalState.score)
        assertEquals("Final state should have correct moves", 5, finalState.moves)
        
        executor.shutdown()
    }

    @Test
    fun `getCurrentGameState should return consistent snapshots during concurrent access`() {
        gameEngine.initializeGame()
        
        val executor = Executors.newFixedThreadPool(5)
        val latch = CountDownLatch(50)
        val states = mutableListOf<GameState>()
        val exceptions = mutableListOf<Exception>()
        
        // Continuously read state while it might be changing
        repeat(50) {
            executor.submit {
                try {
                    val state = gameEngine.getCurrentGameState()
                    synchronized(states) {
                        states.add(state)
                    }
                } catch (e: Exception) {
                    synchronized(exceptions) {
                        exceptions.add(e)
                    }
                } finally {
                    latch.countDown()
                }
            }
        }
        
        assertTrue("State reads should complete within 3 seconds", 
                  latch.await(3, TimeUnit.SECONDS))
        
        assertTrue("No exceptions should occur during state reads: ${exceptions.joinToString()}", 
                  exceptions.isEmpty())
        
        // All states should be valid and consistent
        states.forEach { state ->
            assertTrue("Score should be non-negative", state.score >= 0)
            assertTrue("Moves should be non-negative", state.moves >= 0)
            assertEquals("Board should be 4x4", 4, state.board.size)
            state.board.forEach { row ->
                assertEquals("Each row should have 4 columns", 4, row.size)
            }
        }
        
        executor.shutdown()
    }

    @Test
    fun `setGameState should atomically update all properties`() {
        gameEngine.initializeGame()
        
        val testState = GameState(
            board = Array(4) { Array(4) { null } },
            score = 500,
            moves = 20,
            isGameOver = true,
            hasWon = true
        )
        
        // Set state and immediately read it back
        gameEngine.setGameState(testState)
        val retrievedState = gameEngine.getCurrentGameState()
        
        assertEquals("Score should be updated", 500, retrievedState.score)
        assertEquals("Moves should be updated", 20, retrievedState.moves)
        assertTrue("Game over should be updated", retrievedState.isGameOver)
        assertTrue("Won status should be updated", retrievedState.hasWon)
    }
}
{"formatVersion": 1, "database": {"version": 3, "identityHash": "c98fd99a134d39e4252a03b9e42307c6", "entities": [{"tableName": "game_state", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `board` TEXT NOT NULL, `score` INTEGER NOT NULL, `moves` INTEGER NOT NULL, `isGameOver` INTEGER NOT NULL, `hasWon` INTEGER NOT NULL, `lastMoveType` TEXT, `cumulativePlayTime` INTEGER NOT NULL, `isTimerActive` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "board", "columnName": "board", "affinity": "TEXT", "notNull": true}, {"fieldPath": "score", "columnName": "score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "moves", "columnName": "moves", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isGameOver", "columnName": "isGameOver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "hasWon", "columnName": "hasWon", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastMoveType", "columnName": "lastMoveType", "affinity": "TEXT"}, {"fieldPath": "cumulativePlayTime", "columnName": "cumulativePlayTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTimerActive", "columnName": "isTimerActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "game_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `highScore` INTEGER NOT NULL, `gamesPlayed` INTEGER NOT NULL, `gamesWon` INTEGER NOT NULL, `totalMoves` INTEGER NOT NULL, `highestTile` INTEGER NOT NULL, `bestTile` INTEGER NOT NULL, `averageScore` REAL NOT NULL, `bestTime` INTEGER NOT NULL, `totalPlayTime` INTEGER NOT NULL, `totalMerges` INTEGER NOT NULL, `longestWinStreak` INTEGER NOT NULL, `currentWinStreak` INTEGER NOT NULL, `lastUpdated` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "highScore", "columnName": "highScore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gamesPlayed", "columnName": "gamesPlayed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gamesWon", "columnName": "gamesWon", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalMoves", "columnName": "totalMoves", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "highestTile", "columnName": "highestTile", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bestTile", "columnName": "bestTile", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageScore", "columnName": "averageScore", "affinity": "REAL", "notNull": true}, {"fieldPath": "bestTime", "columnName": "bestTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPlayTime", "columnName": "totalPlayTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalMerges", "columnName": "totalMerges", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longestWinStreak", "columnName": "longestWinStreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentWinStreak", "columnName": "currentWinStreak", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdated", "columnName": "lastUpdated", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c98fd99a134d39e4252a03b9e42307c6')"]}}